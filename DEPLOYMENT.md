# Naroop Deployment Instructions

## Quick Start
1. Run setup: `npm run setup`
2. Review .env file and update configuration
3. Install optional production dependencies: `npm install helmet compression express-rate-limit dotenv`
4. Start production server: `npm run prod`

## Production Deployment

### Prerequisites
- Node.js 14+ installed
- PM2 or systemd for process management
- Nginx for reverse proxy (recommended)
- SSL certificate (Let's Encrypt recommended)

### Environment Setup
1. Copy .env.example to .env
2. Update all configuration values
3. Set strong, unique secrets for SESSION_SECRET, CSRF_SECRET, JWT_SECRET

### Process Management with PM2
```bash
npm install -g pm2
pm2 start server.js --name naroop
pm2 startup
pm2 save
```

### Systemd Service (Linux)
```bash
sudo cp naroop.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable naroop
sudo systemctl start naroop
```

### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### SSL with Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### Monitoring
- Health check: `curl http://localhost:3000/health`
- View logs: `npm run logs`
- Monitor with PM2: `pm2 monit`

### Security Checklist
- [ ] Updated all default secrets in .env
- [ ] Enabled HTTPS
- [ ] Configured firewall
- [ ] Set up regular backups
- [ ] Enabled rate limiting
- [ ] Configured security headers
- [ ] Set up monitoring/alerting

### Backup Strategy
- Backup data files: users.json, posts.json, notifications.json, messages.json
- Backup .env configuration
- Regular automated backups recommended
