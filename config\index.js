const path = require('path');

// Load environment variables from .env file if it exists
try {
    require('dotenv').config();
} catch (error) {
    console.warn('dotenv not installed or .env file not found. Using environment variables only.');
}

const config = {
    // Environment
    NODE_ENV: process.env.NODE_ENV || 'development',
    
    // Server Configuration
    server: {
        port: parseInt(process.env.PORT) || 3000,
        host: process.env.HOST || 'localhost',
        enableCompression: process.env.ENABLE_COMPRESSION === 'true',
        enableCache: process.env.ENABLE_CACHE === 'true',
        cacheTTL: parseInt(process.env.CACHE_TTL) || 300
    },
    
    // Security Configuration
    security: {
        sessionSecret: process.env.SESSION_SECRET || 'default-session-secret-change-in-production',
        csrfSecret: process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production',
        jwtSecret: process.env.JWT_SECRET || 'default-jwt-secret-change-in-production',
        enableHelmet: process.env.ENABLE_HELMET !== 'false',
        enableCors: process.env.ENABLE_CORS !== 'false',
        corsOrigin: process.env.CORS_ORIGIN || '*'
    },
    
    // Rate Limiting
    rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100
    },
    
    // File Upload Configuration
    upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB
        allowedTypes: process.env.ALLOWED_FILE_TYPES ? 
            process.env.ALLOWED_FILE_TYPES.split(',') : 
            ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
    },
    
    // Logging Configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info',
        file: process.env.LOG_FILE || 'logs/naroop.log',
        enableConsole: process.env.NODE_ENV !== 'production',
        enableFile: process.env.NODE_ENV === 'production'
    },
    
    // External Services
    services: {
        geminiApiKey: process.env.GEMINI_API_KEY
    },

    // Firebase Configuration
    firebase: {
        apiKey: process.env.FIREBASE_API_KEY,
        authDomain: process.env.FIREBASE_AUTH_DOMAIN,
        projectId: process.env.FIREBASE_PROJECT_ID,
        storageBucket: process.env.FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.FIREBASE_APP_ID,
        measurementId: process.env.FIREBASE_MEASUREMENT_ID
    },
    
    // Database Configuration (for future use)
    database: {
        url: process.env.DATABASE_URL,
        redis: process.env.REDIS_URL
    },
    
    // Email Configuration (for future use)
    email: {
        smtp: {
            host: process.env.SMTP_HOST,
            port: parseInt(process.env.SMTP_PORT) || 587,
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS
        }
    },
    
    // Monitoring
    monitoring: {
        enableMetrics: process.env.ENABLE_METRICS === 'true',
        metricsPort: parseInt(process.env.METRICS_PORT) || 9090
    },
    
    // Development Configuration
    development: {
        enableDebug: process.env.ENABLE_DEBUG === 'true',
        enableHotReload: process.env.ENABLE_HOT_RELOAD === 'true'
    },
    
    // Data Storage Paths
    storage: {
        dataDir: path.join(__dirname, '..', 'data'),
        usersFile: path.join(__dirname, '..', 'users.json'),
        postsFile: path.join(__dirname, '..', 'posts.json'),
        notificationsFile: path.join(__dirname, '..', 'notifications.json'),
        messagesFile: path.join(__dirname, '..', 'messages.json'),
        logsDir: path.join(__dirname, '..', 'logs')
    }
};

// Validation for production environment
if (config.NODE_ENV === 'production') {
    const requiredSecrets = ['SESSION_SECRET', 'CSRF_SECRET', 'JWT_SECRET'];
    const missingSecrets = requiredSecrets.filter(secret => 
        !process.env[secret] || process.env[secret].includes('default')
    );
    
    if (missingSecrets.length > 0) {
        console.error('❌ Production Error: Missing or default security secrets:', missingSecrets);
        console.error('Please set proper values for these environment variables in production.');
        process.exit(1);
    }
}

// Helper functions
config.isDevelopment = () => config.NODE_ENV === 'development';
config.isProduction = () => config.NODE_ENV === 'production';
config.isTest = () => config.NODE_ENV === 'test';

module.exports = config;
