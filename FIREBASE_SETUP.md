# Firebase Authentication Setup Guide

This guide will help you configure Firebase Authentication for your Naroop project.

## Prerequisites

1. A Google account
2. Access to the [Firebase Console](https://console.firebase.google.com/)

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter your project name (e.g., "<PERSON><PERSON><PERSON>" or "Naroop-Production")
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project dashboard, click on "Authentication" in the left sidebar
2. Click on the "Get started" button
3. Go to the "Sign-in method" tab
4. Click on "Email/Password"
5. Enable "Email/Password" authentication
6. Click "Save"

## Step 3: Get Your Firebase Configuration

1. In your Firebase project dashboard, click on the gear icon (⚙️) next to "Project Overview"
2. Select "Project settings"
3. Scroll down to the "Your apps" section
4. Click on "Add app" and select the web icon (`</>`)
5. Register your app with a nickname (e.g., "Naroop Web App")
6. Copy the Firebase configuration object that looks like this:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key-here",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id-here",
  measurementId: "your-measurement-id" // Optional
};
```

## Step 4: Update Your Environment Variables

1. Open your `.env` file in the project root
2. Replace the Firebase configuration placeholders with your actual values:

```env
# Firebase Configuration
FIREBASE_API_KEY=your-actual-api-key
FIREBASE_AUTH_DOMAIN=your-project-id.firebaseapp.com
FIREBASE_PROJECT_ID=your-actual-project-id
FIREBASE_STORAGE_BUCKET=your-project-id.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your-actual-sender-id
FIREBASE_APP_ID=your-actual-app-id
FIREBASE_MEASUREMENT_ID=your-actual-measurement-id
```

## Step 5: Configure Authorized Domains (Important!)

1. In Firebase Console, go to "Authentication" > "Settings" > "Authorized domains"
2. Add your domains:
   - For development: `localhost`
   - For production: your actual domain (e.g., `naroop.com`)

## Step 6: Test Your Setup

1. Start your development server: `npm run dev`
2. Open your browser to `http://localhost:3000`
3. Try to register a new account
4. Check the Firebase Console under "Authentication" > "Users" to see if the user was created

## Step 7: Security Rules (Optional but Recommended)

If you plan to use Firestore database later, set up security rules:

1. Go to "Firestore Database" in Firebase Console
2. Click "Create database"
3. Choose "Start in test mode" for now
4. Set up proper security rules based on your needs

## Troubleshooting

### Common Issues:

1. **"Firebase configuration incomplete" error**
   - Make sure all Firebase environment variables are set correctly
   - Restart your development server after updating `.env`

2. **"auth/unauthorized-domain" error**
   - Add your domain to the authorized domains list in Firebase Console

3. **"Firebase Admin SDK not available" warning**
   - This is normal for development. The app will fall back to client-side authentication

4. **Users not appearing in Firebase Console**
   - Check browser console for errors
   - Verify your Firebase configuration is correct

### Getting Help:

- Check the browser console for detailed error messages
- Verify your Firebase project settings
- Make sure your API keys are correct and not expired

## Next Steps

Once Firebase authentication is working:

1. Users will be automatically synced between Firebase and your local database
2. Authentication state will persist across browser sessions
3. You can manage users through the Firebase Console
4. Consider setting up additional authentication providers (Google, Facebook, etc.)

## Security Notes

- Never commit your actual Firebase configuration to version control
- Use environment variables for all sensitive configuration
- Consider using different Firebase projects for development and production
- Regularly review your Firebase security rules and user access
