const fs = require('fs');
const path = require('path');
const config = require('../config');

// Ensure logs directory exists
const logsDir = config.storage.logsDir;
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Log levels
const LOG_LEVELS = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3
};

// Colors for console output
const COLORS = {
    error: '\x1b[31m', // Red
    warn: '\x1b[33m',  // Yellow
    info: '\x1b[36m',  // Cyan
    debug: '\x1b[35m', // Magenta
    reset: '\x1b[0m'   // Reset
};

class Logger {
    constructor() {
        this.level = LOG_LEVELS[config.logging.level] || LOG_LEVELS.info;
        this.enableConsole = config.logging.enableConsole;
        this.enableFile = config.logging.enableFile;
        this.logFile = config.logging.file;
    }

    formatMessage(level, message, meta = {}) {
        const timestamp = new Date().toISOString();
        const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
        return `[${timestamp}] [${level.toUpperCase()}] ${message}${metaStr}`;
    }

    writeToFile(formattedMessage) {
        if (this.enableFile) {
            try {
                fs.appendFileSync(this.logFile, formattedMessage + '\n');
            } catch (error) {
                console.error('Failed to write to log file:', error.message);
            }
        }
    }

    writeToConsole(level, formattedMessage) {
        if (this.enableConsole) {
            const color = COLORS[level] || COLORS.reset;
            console.log(`${color}${formattedMessage}${COLORS.reset}`);
        }
    }

    log(level, message, meta = {}) {
        if (LOG_LEVELS[level] <= this.level) {
            const formattedMessage = this.formatMessage(level, message, meta);
            this.writeToConsole(level, formattedMessage);
            this.writeToFile(formattedMessage);
        }
    }

    error(message, meta = {}) {
        this.log('error', message, meta);
    }

    warn(message, meta = {}) {
        this.log('warn', message, meta);
    }

    info(message, meta = {}) {
        this.log('info', message, meta);
    }

    debug(message, meta = {}) {
        this.log('debug', message, meta);
    }

    // Request logging middleware
    requestLogger() {
        return (req, res, next) => {
            const start = Date.now();
            const originalSend = res.send;

            res.send = function(data) {
                const duration = Date.now() - start;
                const logData = {
                    method: req.method,
                    url: req.url,
                    status: res.statusCode,
                    duration: `${duration}ms`,
                    userAgent: req.get('User-Agent'),
                    ip: req.ip || req.connection.remoteAddress
                };

                if (res.statusCode >= 400) {
                    logger.warn(`HTTP ${res.statusCode} ${req.method} ${req.url}`, logData);
                } else {
                    logger.info(`HTTP ${res.statusCode} ${req.method} ${req.url}`, logData);
                }

                originalSend.call(this, data);
            };

            next();
        };
    }

    // Error logging middleware
    errorLogger() {
        return (error, req, res, next) => {
            this.error('Unhandled error', {
                error: error.message,
                stack: error.stack,
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent'),
                ip: req.ip || req.connection.remoteAddress
            });
            next(error);
        };
    }

    // Performance monitoring
    performance(operation, duration, meta = {}) {
        const logData = {
            operation,
            duration: `${duration}ms`,
            ...meta
        };

        if (duration > 1000) {
            this.warn(`Slow operation detected: ${operation}`, logData);
        } else {
            this.debug(`Performance: ${operation}`, logData);
        }
    }

    // Security logging
    security(event, details = {}) {
        this.warn(`Security Event: ${event}`, {
            timestamp: new Date().toISOString(),
            ...details
        });
    }

    // Application lifecycle logging
    startup(message, meta = {}) {
        this.info(`🚀 ${message}`, meta);
    }

    shutdown(message, meta = {}) {
        this.info(`🛑 ${message}`, meta);
    }
}

// Create singleton instance
const logger = new Logger();

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception', {
        error: error.message,
        stack: error.stack
    });
    process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Promise Rejection', {
        reason: reason.toString(),
        promise: promise.toString()
    });
});

module.exports = logger;
