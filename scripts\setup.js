#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

console.log('🚀 Setting up Naroop for production...\n');

// Create necessary directories
const directories = [
    'logs',
    'data',
    'config'
];

directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
    } else {
        console.log(`📁 Directory already exists: ${dir}`);
    }
});

// Generate secure secrets
function generateSecret(length = 64) {
    return crypto.randomBytes(length).toString('hex');
}

// Create .env file if it doesn't exist
const envPath = '.env';
if (!fs.existsSync(envPath)) {
    const envContent = `# Naroop Production Environment Configuration
# Generated on ${new Date().toISOString()}

# Environment
NODE_ENV=production

# Server Configuration
PORT=3000
HOST=0.0.0.0

# Security Configuration (CHANGE THESE IN PRODUCTION!)
SESSION_SECRET=${generateSecret(32)}
CSRF_SECRET=${generateSecret(32)}
JWT_SECRET=${generateSecret(32)}

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp

# Logging
LOG_LEVEL=info
LOG_FILE=logs/naroop.log

# Security Headers
ENABLE_HELMET=true
ENABLE_CORS=true
CORS_ORIGIN=*

# Performance
ENABLE_COMPRESSION=true
ENABLE_CACHE=true
CACHE_TTL=300

# External Services
# GEMINI_API_KEY=your-gemini-api-key-here

# Monitoring
ENABLE_METRICS=false
METRICS_PORT=9090
`;

    fs.writeFileSync(envPath, envContent);
    console.log('✅ Created .env file with secure secrets');
    console.log('⚠️  IMPORTANT: Review and update the .env file with your specific configuration');
} else {
    console.log('📄 .env file already exists');
}

// Initialize data files
const dataFiles = [
    { name: 'users.json', content: '[]' },
    { name: 'posts.json', content: '[]' },
    { name: 'notifications.json', content: '[]' },
    { name: 'messages.json', content: '[]' }
];

dataFiles.forEach(file => {
    if (!fs.existsSync(file.name)) {
        fs.writeFileSync(file.name, file.content);
        console.log(`✅ Created data file: ${file.name}`);
    } else {
        console.log(`📄 Data file already exists: ${file.name}`);
    }
});

// Create a basic systemd service file for Linux deployments
const serviceName = 'naroop.service';
const serviceContent = `[Unit]
Description=Naroop Social Media Platform
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=${process.cwd()}
Environment=NODE_ENV=production
ExecStart=/usr/bin/node server.js
Restart=on-failure
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=naroop

[Install]
WantedBy=multi-user.target
`;

fs.writeFileSync(serviceName, serviceContent);
console.log(`✅ Created systemd service file: ${serviceName}`);

// Create deployment instructions
const deploymentInstructions = `# Naroop Deployment Instructions

## Quick Start
1. Run setup: \`npm run setup\`
2. Review .env file and update configuration
3. Install optional production dependencies: \`npm install helmet compression express-rate-limit dotenv\`
4. Start production server: \`npm run prod\`

## Production Deployment

### Prerequisites
- Node.js 14+ installed
- PM2 or systemd for process management
- Nginx for reverse proxy (recommended)
- SSL certificate (Let's Encrypt recommended)

### Environment Setup
1. Copy .env.example to .env
2. Update all configuration values
3. Set strong, unique secrets for SESSION_SECRET, CSRF_SECRET, JWT_SECRET

### Process Management with PM2
\`\`\`bash
npm install -g pm2
pm2 start server.js --name naroop
pm2 startup
pm2 save
\`\`\`

### Systemd Service (Linux)
\`\`\`bash
sudo cp naroop.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable naroop
sudo systemctl start naroop
\`\`\`

### Nginx Configuration
\`\`\`nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
\`\`\`

### SSL with Let's Encrypt
\`\`\`bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
\`\`\`

### Monitoring
- Health check: \`curl http://localhost:3000/health\`
- View logs: \`npm run logs\`
- Monitor with PM2: \`pm2 monit\`

### Security Checklist
- [ ] Updated all default secrets in .env
- [ ] Enabled HTTPS
- [ ] Configured firewall
- [ ] Set up regular backups
- [ ] Enabled rate limiting
- [ ] Configured security headers
- [ ] Set up monitoring/alerting

### Backup Strategy
- Backup data files: users.json, posts.json, notifications.json, messages.json
- Backup .env configuration
- Regular automated backups recommended
`;

fs.writeFileSync('DEPLOYMENT.md', deploymentInstructions);
console.log('✅ Created deployment instructions: DEPLOYMENT.md');

console.log('\n🎉 Setup complete!');
console.log('\n📋 Next steps:');
console.log('1. Review and update .env file');
console.log('2. Install optional production dependencies: npm install helmet compression express-rate-limit dotenv');
console.log('3. Start the server: npm run prod');
console.log('4. Check health: npm run health');
console.log('5. Read DEPLOYMENT.md for production deployment guide');
console.log('\n⚠️  Remember to secure your secrets and enable HTTPS in production!');
